<?php
/**
 * Application Configuration
 * Hospital Management System
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Application settings
define('APP_NAME', 'MedSystem');
define('APP_VERSION', '1.0.0');
define('BASE_URL', 'http://localhost/dashboard/php_hospital_system');

// Database settings
define('DB_HOST', 'localhost');
define('DB_NAME', 'hospital_management');
define('DB_USER', 'root');
define('DB_PASS', '');

// Security settings
define('CSRF_TOKEN_NAME', 'csrf_token');
define('SESSION_TIMEOUT', 3600); // 1 hour

// Demo mode settings
define('DEMO_MODE', true);

// Demo accounts
$demo_accounts = [
    '<EMAIL>' => [
        'password' => 'admin123',
        'role' => 'admin',
        'full_name' => 'System Administrator',
        'id' => 'admin-001'
    ],
    '<EMAIL>' => [
        'password' => 'doctor123',
        'role' => 'doctor',
        'full_name' => 'Dr. <PERSON>',
        'id' => 'doctor-001'
    ],
    '<EMAIL>' => [
        'password' => 'patient123',
        'role' => 'patient',
        'full_name' => 'Jane Doe',
        'id' => 'patient-001'
    ]
];

// Include required files
require_once __DIR__ . '/database.php';

// Utility functions
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

function isLoggedIn() {
    return isset($_SESSION['user_id']) && isset($_SESSION['user_role']);
}

function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: ' . BASE_URL . '/login.php');
        exit();
    }
}

function hasRole($required_role) {
    if (!isLoggedIn()) {
        return false;
    }
    
    $user_role = $_SESSION['user_role'];
    
    // Admin has access to everything
    if ($user_role === 'admin') {
        return true;
    }
    
    // Check specific role
    if (is_array($required_role)) {
        return in_array($user_role, $required_role);
    }
    
    return $user_role === $required_role;
}

function requireRole($required_role) {
    if (!hasRole($required_role)) {
        header('HTTP/1.1 403 Forbidden');
        die('Access denied. Insufficient permissions.');
    }
}

function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function redirect($url) {
    header('Location: ' . $url);
    exit();
}
?>
