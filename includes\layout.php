<?php
/**
 * Layout Template
 * Hospital Management System
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../classes/Auth.php';

$auth = new Auth();
requireLogin();

$current_user = $auth->getCurrentUser();
$current_page = $current_page ?? 'dashboard';

// Define menu items with role-based access
$menu_items = [
    'dashboard' => [
        'label' => 'Dashboard',
        'icon' => 'activity',
        'roles' => ['admin', 'doctor', 'patient'],
        'url' => 'dashboard.php'
    ],
    'appointments' => [
        'label' => 'Appointments',
        'icon' => 'calendar',
        'roles' => ['admin', 'doctor', 'patient'],
        'url' => 'appointments.php'
    ],
    'doctors' => [
        'label' => 'Doctors',
        'icon' => 'stethoscope',
        'roles' => ['admin'],
        'url' => 'doctor_management.php'
    ],
    'patients' => [
        'label' => 'Patients',
        'icon' => 'users',
        'roles' => ['admin', 'doctor'],
        'url' => 'patient_management.php'
    ],
    'departments' => [
        'label' => 'Departments',
        'icon' => 'building',
        'roles' => ['admin'],
        'url' => 'department_management.php'
    ],
    'rooms' => [
        'label' => 'Rooms',
        'icon' => 'bed',
        'roles' => ['admin'],
        'url' => 'room_management.php'
    ],
    'users' => [
        'label' => 'Users',
        'icon' => 'user-plus',
        'roles' => ['admin'],
        'url' => 'user_management.php'
    ],
    'profile' => [
        'label' => 'Profile',
        'icon' => 'settings',
        'roles' => ['admin', 'doctor', 'patient'],
        'url' => 'profile.php'
    ]
];

// Filter menu items based on user role
$visible_menu_items = array_filter($menu_items, function($item) use ($current_user) {
    return in_array($current_user['role'], $item['roles']);
});

function getIcon($icon_name) {
    $icons = [
        'activity' => '<path d="M22 12h-4l-3 9L9 3l-3 9H2"/>',
        'calendar' => '<rect x="3" y="4" width="18" height="18" rx="2" ry="2"/><line x1="16" y1="2" x2="16" y2="6"/><line x1="8" y1="2" x2="8" y2="6"/><line x1="3" y1="10" x2="21" y2="10"/>',
        'stethoscope' => '<path d="M4.8 2.3A.3.3 0 1 0 5 2H4a2 2 0 0 0-2 2v5a6 6 0 0 0 6 6v0a6 6 0 0 0 6-6V4a2 2 0 0 0-2-2h-1a.2.2 0 1 0 .2.3"/><path d="M8 15v1a6 6 0 0 0 6 6v0a6 6 0 0 0 6-6v-4"/>',
        'users' => '<path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M23 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/>',
        'building' => '<path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"/><path d="M6 12h4"/><path d="M6 16h4"/><path d="M16 12h2"/><path d="M16 16h2"/>',
        'bed' => '<path d="M2 4v16"/><path d="M2 8h18a2 2 0 0 1 2 2v10"/><path d="M2 17h20"/><path d="M6 8v9"/>',
        'user-plus' => '<path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><line x1="19" y1="8" x2="19" y2="14"/><line x1="22" y1="11" x2="16" y2="11"/>',
        'settings' => '<circle cx="12" cy="12" r="3"/><path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>',
        'log-out' => '<path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/><polyline points="16,17 21,12 16,7"/><line x1="21" y1="12" x2="9" y2="12"/>'
    ];
    
    return $icons[$icon_name] ?? $icons['activity'];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title ?? APP_NAME; ?></title>
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/style.css">
</head>
<body class="dashboard-layout">
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-brand">
                <div class="logo-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <?php echo getIcon('activity'); ?>
                    </svg>
                </div>
                <div>
                    <h1><?php echo APP_NAME; ?></h1>
                    <p><?php echo htmlspecialchars($current_user['role']); ?></p>
                </div>
            </div>
        </div>

        <nav class="sidebar-nav">
            <?php foreach ($visible_menu_items as $key => $item): ?>
                <a href="<?php echo BASE_URL . '/' . $item['url']; ?>" 
                   class="nav-item <?php echo $current_page === $key ? 'active' : ''; ?>">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <?php echo getIcon($item['icon']); ?>
                    </svg>
                    <?php echo $item['label']; ?>
                </a>
            <?php endforeach; ?>
        </nav>

        <div class="sidebar-footer">
            <a href="<?php echo BASE_URL; ?>/logout.php" class="nav-item">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <?php echo getIcon('log-out'); ?>
                </svg>
                Sign Out
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <?php if (isset($page_title)): ?>
        <div class="page-header">
            <h1 class="page-title"><?php echo htmlspecialchars($page_title); ?></h1>
            <?php if (isset($page_subtitle)): ?>
                <p class="page-subtitle"><?php echo htmlspecialchars($page_subtitle); ?></p>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <!-- Page Content -->
        <div class="page-content">
            <?php echo $content ?? ''; ?>
        </div>
    </div>

    <script src="<?php echo BASE_URL; ?>/assets/js/main.js"></script>
</body>
</html>
