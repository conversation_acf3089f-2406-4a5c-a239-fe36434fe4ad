<?php
/**
 * Appointments Page
 * Hospital Management System
 */

require_once 'config/config.php';
require_once 'classes/Auth.php';
require_once 'classes/Dashboard.php';

$auth = new Auth();
requireLogin();

$current_user = $auth->getCurrentUser();
$dashboard = new Dashboard();
$appointments = $dashboard->getRecentAppointments(20); // Get more appointments

$current_page = 'appointments';
$page_title = 'Appointments';
$page_subtitle = 'View and manage appointments';

function getStatusBadge($status) {
    $badges = [
        'scheduled' => 'badge-info',
        'completed' => 'badge-success',
        'cancelled' => 'badge-danger'
    ];
    
    return $badges[$status] ?? 'badge-info';
}

function formatTime($time) {
    return date('g:i A', strtotime($time));
}

function formatDate($date) {
    return date('M j, Y', strtotime($date));
}

ob_start();
?>

<!-- Appointments Table -->
<div class="card">
    <div class="card-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h2 class="card-title">All Appointments (<?php echo count($appointments); ?>)</h2>
            <?php if ($current_user['role'] === 'admin'): ?>
                <button type="button" class="btn btn-primary">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="12" y1="5" x2="12" y2="19"/>
                        <line x1="5" y1="12" x2="19" y2="12"/>
                    </svg>
                    Schedule Appointment
                </button>
            <?php endif; ?>
        </div>
    </div>
    <div class="card-body">
        <?php if (empty($appointments)): ?>
            <p style="text-align: center; color: #6b7280; padding: 2rem;">No appointments found.</p>
        <?php else: ?>
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Time</th>
                            <th>Patient</th>
                            <th>Doctor</th>
                            <th>Specialization</th>
                            <th>Status</th>
                            <th>Notes</th>
                            <?php if ($current_user['role'] === 'admin'): ?>
                                <th>Actions</th>
                            <?php endif; ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($appointments as $appointment): ?>
                            <tr>
                                <td><?php echo formatDate($appointment['appointment_date']); ?></td>
                                <td><?php echo formatTime($appointment['appointment_time']); ?></td>
                                <td><?php echo htmlspecialchars($appointment['patient_name']); ?></td>
                                <td><?php echo htmlspecialchars($appointment['doctor_name']); ?></td>
                                <td><?php echo htmlspecialchars($appointment['specialization']); ?></td>
                                <td>
                                    <span class="badge <?php echo getStatusBadge($appointment['status']); ?>">
                                        <?php echo ucfirst($appointment['status']); ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($appointment['notes'] ?? '-'); ?></td>
                                <?php if ($current_user['role'] === 'admin'): ?>
                                    <td>
                                        <div style="display: flex; gap: 0.5rem;">
                                            <button type="button" class="btn btn-secondary btn-sm">
                                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                                                </svg>
                                            </button>
                                            <button type="button" class="btn btn-danger btn-sm">
                                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <circle cx="12" cy="12" r="10"/>
                                                    <line x1="15" y1="9" x2="9" y2="15"/>
                                                    <line x1="9" y1="9" x2="15" y2="15"/>
                                                </svg>
                                            </button>
                                        </div>
                                    </td>
                                <?php endif; ?>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
$content = ob_get_clean();
include 'includes/layout.php';
?>
