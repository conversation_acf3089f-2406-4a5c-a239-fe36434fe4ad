<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200">
  <!-- Background circle with gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="crossGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Main circle background -->
  <circle cx="100" cy="100" r="90" fill="url(#bgGradient)" stroke="#ffffff" stroke-width="4"/>

  <!-- Inner circle -->
  <circle cx="100" cy="100" r="75" fill="#ffffff" stroke="#e2e8f0" stroke-width="2"/>

  <!-- Medical cross -->
  <g transform="translate(100,100)">
    <!-- Vertical bar of cross -->
    <rect x="-8" y="-35" width="16" height="70" rx="8" fill="url(#crossGradient)" stroke="#2563eb" stroke-width="1"/>
    <!-- Horizontal bar of cross -->
    <rect x="-35" y="-8" width="70" height="16" rx="8" fill="url(#crossGradient)" stroke="#2563eb" stroke-width="1"/>

    <!-- Small medical symbols -->
    <!-- Heart symbol -->
    <g transform="translate(-20,-20) scale(0.8)">
      <path d="M12,21.35l-1.45-1.32C5.4,15.36,2,12.28,2,8.5 C2,5.42,4.42,3,7.5,3c1.74,0,3.41,0.81,4.5,2.09C13.09,3.81,14.76,3,16.5,3 C19.58,3,22,5.42,22,8.5c0,3.78-3.4,6.86-8.55,11.54L12,21.35z" fill="#ef4444"/>
    </g>

    <!-- Stethoscope symbol -->
    <g transform="translate(20,20) scale(0.6)">
      <circle cx="6" cy="6" r="3" fill="none" stroke="#10b981" stroke-width="2"/>
      <path d="M6,9 Q6,15 12,15 Q18,15 18,9" fill="none" stroke="#10b981" stroke-width="2"/>
      <circle cx="18" cy="9" r="2" fill="#10b981"/>
    </g>

    <!-- Plus symbol in center -->
    <circle cx="0" cy="0" r="12" fill="#2563eb"/>
    <rect x="-6" y="-2" width="12" height="4" fill="#ffffff"/>
    <rect x="-2" y="-6" width="4" height="12" fill="#ffffff"/>
  </g>

  <!-- Decorative elements -->
  <circle cx="50" cy="50" r="3" fill="#60a5fa" opacity="0.6"/>
  <circle cx="150" cy="50" r="3" fill="#60a5fa" opacity="0.6"/>
  <circle cx="50" cy="150" r="3" fill="#60a5fa" opacity="0.6"/>
  <circle cx="150" cy="150" r="3" fill="#60a5fa" opacity="0.6"/>

  <!-- Text -->
  <text x="100" y="180" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2563eb">MEDICAL</text>
</svg>
