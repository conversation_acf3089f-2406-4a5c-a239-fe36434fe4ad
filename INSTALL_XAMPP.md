# 🏥 تعليمات التثبيت على XAMPP

## 📋 الخطوات السريعة:

### 1️⃣ **نسخ الملفات:**
انسخ مجلد `php_hospital_system` إلى:
```
C:\xampp\htdocs\dashboard\php_hospital_system
```

### 2️⃣ **تشغيل XAMPP:**
- افتح XAMPP Control Panel
- شغل **Apache** و **MySQL**

### 3️⃣ **اختبار النظام:**
اذهب إلى:
```
http://localhost/dashboard/php_hospital_system/test_system.php
```

### 4️⃣ **تشغيل معالج الإعداد:**
```
http://localhost/dashboard/php_hospital_system/setup.php
```

### 5️⃣ **إعدادات قاعدة البيانات:**
- **Host:** localhost
- **Database:** hospital_management  
- **Username:** root
- **Password:** (اتركه فارغ)

### 6️⃣ **تسجيل الدخول:**
```
http://localhost/dashboard/php_hospital_system/login.php
```

## 👥 **الحسابات التجريبية:**

| الدور | البريد الإلكتروني | كلمة المرور |
|-------|------------------|-------------|
| مدير النظام | <EMAIL> | admin123 |
| طبيب | <EMAIL> | doctor123 |
| مريض | <EMAIL> | patient123 |

## 🔧 **حل المشاكل الشائعة:**

### ❌ **خطأ 404 - الصفحة غير موجودة:**
- تأكد أن المجلد في المسار الصحيح
- تأكد أن Apache يعمل في XAMPP

### ❌ **خطأ قاعدة البيانات:**
- تأكد أن MySQL يعمل في XAMPP
- شغل معالج الإعداد مرة أخرى

### ❌ **مشاكل CSS/JS:**
- تأكد أن BASE_URL صحيح في `config/config.php`
- امسح cache المتصفح

## 🎯 **الروابط المهمة:**

- **اختبار النظام:** `http://localhost/dashboard/php_hospital_system/test_system.php`
- **معالج الإعداد:** `http://localhost/dashboard/php_hospital_system/setup.php`
- **تسجيل الدخول:** `http://localhost/dashboard/php_hospital_system/login.php`
- **لوحة التحكم:** `http://localhost/dashboard/php_hospital_system/dashboard.php`

## 📞 **الدعم:**
إذا واجهت أي مشكلة، شغل ملف `test_system.php` أولاً لتشخيص المشكلة.

---
**ملاحظة:** النظام يعمل في وضع التجريب حتى بدون قاعدة بيانات! 🚀
